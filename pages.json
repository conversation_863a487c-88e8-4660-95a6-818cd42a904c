{
	"easycom": {
		"^tn-(.*)": "@/tuniao-ui/components/tn-$1/tn-$1.vue",
		"^sign-(.*)": "@/pagesSignUp/components/$1.vue"
	},
	"pages": [

		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "名扬体育学苑",
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/paper/index",
			"style": {
				"navigationBarTitleText": "模拟考试",
				"enablePullDownRefresh": false,
				// 距离底部50px时，触发onReachBottom事件
				"onReachBottonDistance": 50
			}

		}, {
			"path": "pages/paper/paper",
			"style": {
				"navigationBarTitleText": "考试试卷",
				"enablePullDownRefresh": false,
				"autoBackButton": false,
				"navigationStyle": "custom",
				"app-plus": {
					"popGesture": "none"
				}
				// "navigationBarBackgroundColor": "#FFFFFF",
				// "navigationBarTextStyle": "black",
				// "titleNView": false,
				// "navigationStyle": "custom"
			}

		}, {
			"path": "pages/paper/log",
			"style": {
				"navigationBarTitleText": "考试记录",
				"enablePullDownRefresh": false,
				// 距离底部50px时，触发onReachBottom事件
				"onReachBottonDistance": 50
			}

		}, {
			"path": "pages/collect/index",
			"style": {
				"navigationBarTitleText": "收藏题目",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/wrong/index",
			"style": {
				"navigationBarTitleText": "错题记录",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/search/index",
			"style": {
				"navigationBarTitleText": "题目搜索",
				"enablePullDownRefresh": false
				// "style": {
				// 	"navigationBarTitleText": "",
				// 	"navigationStyle": "custom"
				// }
			}

		}, {
			"path": "pages/paper/rank",
			"style": {
				"navigationBarTitleText": "排行榜",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/paper/grade",
			"style": {
				"navigationBarTitleText": "考试成绩",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/train/train",
			"style": {
				"navigationBarTitleText": "答题",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/train/index",
			"style": {
				"navigationBarTitleText": "题目选择",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/train/look",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/train/detail",
			"style": {
				"navigationBarTitleText": "题目选择",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/webview/webview",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/index/notice-list",
			"style": {
				"navigationBarTitleText": "考试公示",
				"enablePullDownRefresh": false,
				// 距离底部50px时，触发onReachBottom事件
				"onReachBottonDistance": 50
				// "navigationBarTitleText": "",
				// "navigationStyle": "custom"
			}

		}, {
			"path": "pages/index/notice-detail",
			"style": {
				"navigationBarTitleText": "详情",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/index/news-list",
			"style": {
				"navigationBarTitleText": "关于我们",
				"enablePullDownRefresh": false,
				// 距离底部50px时，触发onReachBottom事件
				"onReachBottonDistance": 50
			}

		}, {
			"path": "pages/index/news-detail",
			"style": {
				"navigationBarTitleText": "详情",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/score/good-detail",
			"style": {
				"navigationBarTitleText": "积分详情",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/score/good-list",
			"style": {
				"navigationBarTitleText": "积分详情",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/score/order-detail",
			"style": {
				"navigationBarTitleText": "订单详情",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/score/order-list",
			"style": {
				"navigationBarTitleText": "积分订单",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/score/score-log",
			"style": {
				"navigationBarTitleText": "积分记录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cert/index",
			"style": {
				"navigationBarTitleText": "荣誉证书",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cert/list",
			"style": {
				"navigationBarTitleText": "荣誉证书",
				"enablePullDownRefresh": false
			}
		}
	],
	"subPackages": [
		// 招聘
		// {
		// 	"root": "pagesRecruit",
		// 	"pages": [{
		// 			"path": "index",
		// 			"style": {
		// 				"navigationBarTitleText": "岗位",
		// 				"enablePullDownRefresh": true
		// 			}
		// 		},
		// 		{
		// 			"path": "detail",
		// 			"style": {
		// 				"navigationBarTitleText": "职位详情"
		// 			}
		// 		}
		// 	]
		// },
		// 分类
		{
			"root": "pagesSubject",
			"pages": [{
					"path": "index",
					"style": {
						"navigationBarTitleText": "科目",
						"enablePullDownRefresh": true
					}
				},
				{
					"path": "video/index",
					"style": {
						"navigationBarTitleText": "课程中心"
					}
				},
				{
					"path": "video/list",
					"style": {
						"navigationBarTitleText": "课程中心"
					}
				},
				{
					"path": "video/info",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "video/user",
					"style": {
						"navigationBarTitleText": "学习记录"
					}
				},
				{
					"path": "video/userInfo",
					"style": {
						"navigationBarTitleText": "视频详情"
					}
				}
			]
		},
		// 考场
		{
			"root": "pages/room/",
			"pages": [{
				"path": "index",
				"style": {
					"navigationBarTitleText": "考场列表",
					"enablePullDownRefresh": false
					// "navigationStyle": "custom"
				}
			}, {
				"path": "detail",
				"style": {
					"navigationBarTitleText": "考场详情",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "signup-index",
				"style": {
					"navigationBarTitleText": "报名记录",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "grade",
				"style": {
					"navigationBarTitleText": "考场成绩",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "rank",
				"style": {
					"navigationBarTitleText": "考场排行榜",
					"enablePullDownRefresh": false
				}
			}]
		},
		// 用户
		{
			"root": "pages/user/",
			"pages": [{
				"path": "user",
				"style": {
					// "navigationBarTitleText": "用户中心",
					// "enablePullDownRefresh": false,
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}
			}, {
				"path": "set",
				"style": {
					"navigationBarTitleText": "个人设置",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "my-cate",
				"style": {
					"navigationBarTitleText": "常用题库",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "login-reg",
				"style": {
					"navigationBarTitleText": "登录注册",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}

			}, {
				"path": "member-center",
				"style": {
					"navigationBarTitleText": "会员中心",
					"enablePullDownRefresh": false,
					"navigationStyle": "custom"
				}

			}, {
				"path": "member-protocol",
				"style": {
					"navigationBarTitleText": "会员协议",
					"enablePullDownRefresh": false
					// "navigationStyle": "custom"
				}

			}, {
				"path": "my-correction",
				"style": {
					"navigationBarTitleText": "纠错反馈",
					"enablePullDownRefresh": false
						// #ifdef H5
						,
					"navigationBarHidden": true
					// #endif
				}

			}, {
				"path": "password",
				"style": {
					"navigationBarTitleText": "修改密码",
					"enablePullDownRefresh": false
					// "navigationStyle": "custom"
				}

			}]
		}

	],

	"globalStyle": {
		"mp-alipay": {
			/* 支付宝小程序特有相关 */
			"transparentTitle": "always",
			"allowsBounceVertical": "NO"
		},
		// "navigationBarBackgroundColor": "#0081ff",

		"navigationBarTitleText": "名扬体育学院",
		"navigationBarBackgroundColor": "#1A73E8",
		"navigationBarTextStyle": "white"
		// #ifdef H5
		// ,"navigationStyle": "custom"
		// ,"navigationBarHidden": true
		// #endif
	}
	// ,
	// "usingComponts": true,
	// 	"condition": { //模式配置，仅开发期间生效
	// 	"current": 0, //当前激活的模式(list 的索引项)
	// 	"list": [{
	// 			"name": "表单", //模式名称
	// 			"path": "pages/component/form", //启动页面
	// 			"query": "" //启动参数
	// 		}
	// 	]
	// }

}