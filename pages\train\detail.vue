<template>
	<view style="padding: 30rpx; background-color: #FAFAFA; min-height: 100vh;">
		<view style="display: flex;align-items: center;">
			<view @click="show = true"
				style="display: flex;align-items: center; border: 2rpx solid #1A73E8; border-radius: 32rpx; padding: 8rpx 24rpx; color: #1A73E8; background: #fff; font-weight: 500; box-shadow: 0 2rpx 8rpx rgba(26,115,232,0.08); cursor: pointer; transition: background 0.2s;">
				<view style="margin-right: 8rpx;">更多题库</view>
				<view>
					<text class="tn-icon-down-triangle"
						style="font-size: 24rpx; color: #1A73E8; position: relative;"></text>
				</view>
			</view>
			<view style="display: inline-block; position: relative;padding-left: 30rpx;font-size: 32rpx;font-weight: bold;">
				<text>{{ info.text }}</text>
			</view>
		</view>
		<view style="padding: 20rpx 0px;margin-top: 30rpx;">
			<view v-for="item in info.children" style="margin-bottom: 40rpx;">
				<view @click="goTrain(item.value, item.text, item.type)"
					style="display: flex;justify-content: space-between;align-items: center; background: #fff; border-radius: 16rpx; box-shadow: 0 4rpx 16rpx rgba(26,115,232,0.06); padding: 30rpx;">
					<view style="display: flex;align-items: center;">
						<view
							style="height: 40rpx;width: 40rpx;background-color: #1A73E8;border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #fff; font-size: 24rpx; font-weight: bold;">
							题
						</view>
						<view style="padding-left: 32rpx;">
							<view style="font-size: 32rpx; font-weight: bold; color: #222;">{{ item.text }}</view>
							<view style="color: #999999;font-size: 24rpx;padding-top: 8rpx;">题目数量：{{ item.count }}道
							</view>
						</view>
					</view>
					<!-- 1会员 2不是会员 -->
					<view v-if="item.type == 2"
						style="background: #1A73E8; color: #fff; border-radius: 24rpx; padding: 4rpx 28rpx; font-size: 26rpx; font-weight: 500; letter-spacing: 2rpx;">
						免费</view>
					<view v-else
						style="background: #f39c12; color: #fff; border-radius: 24rpx; padding: 4rpx 28rpx; font-size: 26rpx; font-weight: 500; letter-spacing: 2rpx;">
						会员</view>
				</view>
			</view>
		</view>
		<tn-popup v-model="show" mode="left" width="550rpx"
			:style="'border-radius:32rpx 0 0 32rpx; box-shadow: 0 8rpx 32rpx rgba(26,115,232,0.12); overflow: hidden;'">
			<view style="padding: 30rpx;">
				<view v-for="item in cateList" @click="getTree(item)"
					style="margin-bottom: 40rpx;padding:25rpx 20rpx;text-align: center;background-color: #F3F5F7;border-radius: 60rpx; font-size: 28rpx; color: #1A73E8;">
					{{ item.text }}</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
import newsApi from "@/common/api/news.js"
export default {
	data() {
		return {
			show: false,
			id: 0,
			cateList: [],
			info: {},
			vip: 0,
		}
	},
	onLoad(op) {
		console.log(op);
		this.id = op.id;
		this.getCate();
		this.getIsUser();
	},
	methods: {
		getTree(item) {
			this.info = item;
			this.show = false;
		},
		getIsUser() {
			newsApi.ifUserMember(this, {}).then(res => {
				//0 不是会员  
				console.log(res);
				this.vip = res.code;
			})
		},
		getCate() {
			this.http('cate/getThree', {
				kind: 'QUESTION',
			}).then(res => {
				this.cateList = res.data;
				this.info = res.data.find(item => item.value == this.id);
			})
		},
		goTrain(id, name, type) {
			if (type == 1 && this.vip == 0) {
				uni.showModal({
					title: '提示',
					content: '该题库仅限会员可用，请联系客服开通会员！',
					confirmText: '联系客服',
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: '18903795988' //仅为示例
							});
						}
					}
				});
				return;
			}
			this.utils.goto('train?cateId=' + id + '&cateName=' + name + '&mode=normal')
		}
	}
}
</script>

<style>
page {
	background-color: #FAFAFA;
}

/* 统一字体和颜色 */
body,
view,
text {
	font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
	color: #222;
}

/* 主题色变量可用于后续扩展 */
:root {
	--theme-color: #1A73E8;
}

/* 适当增加全局间距 */
view {
	box-sizing: border-box;
}

/* 注释：如需进一步美化可在此扩展 */
</style>
